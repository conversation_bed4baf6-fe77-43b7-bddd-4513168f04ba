**********************************************************************
** Copyright Amazon.com, Inc. or its affiliates.
** All Rights Reserved.
**
** Licensed under the Apache License, Version 2.0 (the "License").
** You may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**    http:**www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing,
** software distributed under the License is distributed on an
** "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
** either express or implied. See the License for the specific
** language governing permissions and limitations under the License
**********************************************************************

1LJOB,JOB=CLOSEFIL,LIST=ALL
JOB=CLOSEFIL LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

CLOSEFIL 255 CLOSEFIL CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=ACCTFILE SCHID=030      QTM=0100 LEADTM=0000 SUBMTM=

1LJOB,JOB=ACCTFILE,LIST=ALL
JOB=ACCTFILE LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

ACCTFILE 255 ACCTFILE CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=CARDFILE SCHID=030      QTM=0100 LEADTM=0000 SUBMTM=

1LJOB,JOB=CARDFILE,LIST=ALL
JOB=CARDFILE LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

CARDFILE 255 CARDFILE CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=XREFFILE SCHID=030      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=XREFFILE,LIST=ALL
JOB=XREFFILE LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

XREFFILE 255 XREFFILE CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=CUSTFILE SCHID=030      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=CUSTFILE,LIST=ALL
JOB=CUSTFILE LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

CUSTFILE 255 CUSTFILE CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=TRANBKP SCHID=030      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=TRANBKP,LIST=ALL
JOB=TRANBKP LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

TRANBKP 255 TRANBKP CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=DISCGRP SCHID=030      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=DISCGRP,LIST=ALL
JOB=DISCGRP LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

DISCGRP 255 DISCGRP CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=TCATBALF SCHID=030      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=TCATBALF,LIST=
JOB=TCATBALF LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

TCATBALF 255 TCATBALF CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=TRANTYPE SCHID=030      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=TRANTYPE,LIST=ALL
JOB=TRANTYPE LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

TRANTYPE 255 TRANTYPE CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=DUSRSECJ SCHID=030      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=DUSRSECJ,LIST=ALL
JOB=DUSRSECJ LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

DUSRSECJ 255 DUSRSECJ CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=POSTTRAN SCHID=030      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=POSTTRAN,LIST=ALL
JOB=POSTTRAN LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

POSTTRAN 255 POSTTRAN CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=INTCALC SCHID=030      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=INTCALC,LIST=ALL
JOB=INTCALC LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

INTCALC 255 INTCALC CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=TRANBKP SCHID=031      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=TRANBKP,LIST=ALL

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=COMBTRAN SCHID=031      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=COMBTRAN,LIST=ALL
JOB=COMBTRAN LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

COMBTRAN 255 COMBTRAN CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=CREASTMT SCHID=031      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=CREASTMT,LIST=ALL
JOB=CREASTMT LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

CREASTMT 255 CREASTMT CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=COMBTRAN SCHID=032      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=COMBTRAN,LIST=ALL

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=CREASTMT SCHID=032      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=CREASTMT,LIST=ALL

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=TRANIDX SCHID=032      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=TRANIDX,LIST=ALL
JOB=TRANIDX LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

TRANIDX 255 TRANIDX CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      Y -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000

    --------------------------- TRIGGERED JOBS ----------------------------
       JOB=OPENFIL SCHID=032      QTM=0100 LEADTM=0000 SUBMTM=0000

1LJOB,JOB=OPENFIL,LIST=ALL
JOB=OPENFIL LIST=ALL                                  DATE=06.198    PAGE 0001

JOB    ----JCL----   SYSTEM  USR MAIN PROSE  SCHED  --NUMBER OF-   LAST-RUN
NAME    ID  MEMBER   -NAME-  -ID -ID- DSNBR  DSNBR  STP DDS RUNS   DATE/TIME

OPENFIL 255 OPENFIL CARDDEMO  000 ALL  NONE 000652 005 000 1058  06198/0700

    --------------------------- JOB INFORMATION ---------------------------
    N -- SCHD RESOLUTION REQUIRED      N -- LOAD STEP TO BE EXECUTED
    N -- OVERRIDE OF JCL REQUIRED      Y -- JOB MARKED AS MAINT ONLY
    N -- MANUAL VERIFICATION REQD      N -- JOB SET FOR HOLD IN REQQ
    Y -- REQUIREMNTS TO BE LISTED      N -- COMP TRIGGERS OTHER JOBS
    Y -- AUTO-GENERATION OF 7 RMS      N -- JOB ELIGIBLE FOR PROMPTS
    Y -- ERRORS FOR RQMT NOT USED      Y -- JOB SET FOR EXEC ON MAIN
    Y -- ERRORS FOR DSN NOT FOUND      N -- JCL TO BE KEPT IN PRRN/Q

    . OWNER= *NONE*  JCLLIB=&CARDDEMOPRODJCL  ARFSET= *NONE*
    . LAST MAINTENANCE ON 03.237 AT 07:00:00 VIA LOAD
    . CLASS=,MSGCLASS=A,REGION=4096K,PRTY=000,CPUTM=00023,ELAPTM=2359
    . TAPE1: CALC=000,MANL=000,TAPE2: CALC=000,MANL=000
    . DONT SCHEDULE BEFORE 03237 AT 0000
    . # OF TIMES LATE = 0008    # OF TIMES RESTARTED = 0000
