//WAITSTEP JOB 'WAIT STEP',CLASS=A,MSGCLASS=0,
// NOTIFY=&SYSUID
//******************************************************************
//* Copyright Amazon.com, Inc. or its affiliates.
//* All Rights Reserved.
//*
//* Licensed under the Apache License, Version 2.0 (the "License").
//* You may not use this file except in compliance with the License.
//* You may obtain a copy of the License at
//*
//*    http://www.apache.org/licenses/LICENSE-2.0
//*
//* Unless required by applicable law or agreed to in writing,
//* software distributed under the License is distributed on an
//* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
//* either express or implied. See the License for the specific
//* language governing permissions and limitations under the License
//******************************************************************
//* *******************************************************************
//* WAIT FOR CENTISECONDS IN THE PARM EG: 00003600  = 36 SECONDS
//* *******************************************************************
//WAIT     EXEC PGM=COBSWAIT                                            00040001
//STEPLIB  DD DSN=AWS.M2.CARDDEMO.LOADLIB,DISP=SHR                      00050000
//SYSOUT DD SYSOUT=*                                                    00060000
//SYSIN    DD *                                                         00070000
00003600      VALUE IN CENTISECONDS                                     00080000
/*                                                                      00080000
