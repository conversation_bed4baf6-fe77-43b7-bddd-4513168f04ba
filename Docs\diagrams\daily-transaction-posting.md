# Daily Transaction Posting Process (CBTRN02C)

## Overview
This diagram shows the batch process that validates and posts daily transactions from external sources to the transaction master file and updates account balances.

## Business Purpose
- Process daily transaction files in batch mode
- Validate all transactions before posting
- Update account balances and category balances
- Generate reject file for invalid transactions

## Mermaid Diagram Code

```mermaid
flowchart TD
    A[Start CBTRN02C] --> B[Open All Files]
    B --> C[Initialize Counters]
    C --> D[Read Daily Transaction File]
    D --> E{End of File?}
    E -->|Yes| F[Display Statistics]
    E -->|No| G[Increment Transaction Count]
    G --> H[Initialize Validation Flags]
    H --> I[Validate Transaction]
    
    I --> J[Lookup Card in XREF]
    J --> K{Card Found?}
    K -->|No| L[Set Error 100<br/>Invalid Card]
    K -->|Yes| M[Lookup Account]
    M --> N{Account Found?}
    N -->|No| O[Set Error 101<br/>Account Not Found]
    N -->|Yes| P[Check Credit Limit]
    P --> Q[Calculate Available Credit]
    Q --> R{Amount <= Available?}
    R -->|No| S[Set Error 102<br/>Over Limit]
    R -->|Yes| T[Check Account Expiration]
    T --> U{Account Valid?}
    U -->|No| V[Set Error 103<br/>Account Expired]
    U -->|Yes| W[Validation Passed]
    
    L --> X{Validation OK?}
    O --> X
    S --> X
    V --> X
    W --> X
    
    X -->|No| Y[Write to Reject File]
    X -->|Yes| Z[Post Transaction]
    
    Y --> AA[Increment Reject Count]
    Z --> BB[Update Account Balance]
    BB --> CC[Update Category Balance]
    CC --> DD[Write to Transaction Master]
    
    AA --> D
    DD --> D
    
    F --> EE[Close All Files]
    EE --> FF[End Process]
    
    style A fill:#e1f5fe
    style W fill:#c8e6c9
    style Z fill:#c8e6c9
    style BB fill:#c8e6c9
    style CC fill:#c8e6c9
    style DD fill:#c8e6c9
    style L fill:#ffcdd2
    style O fill:#ffcdd2
    style S fill:#ffcdd2
    style V fill:#ffcdd2
    style Y fill:#ffcdd2
    style FF fill:#e8f5e8
```

## File Processing
- **Input**: DALYTRAN (Daily transaction file)
- **Output**: TRANSACT (Transaction master file)
- **Reject**: DALYREJS (Rejected transactions)
- **Updates**: ACCTFILE (Account balances), TCATBALF (Category balances)

## Validation Rules
1. **Card Validation**: Must exist in CARDXREF file
2. **Account Validation**: Must exist and be active
3. **Credit Limit**: Transaction amount cannot exceed available credit
4. **Account Expiration**: Transaction date must be before account expiration

## Error Handling
- Invalid transactions written to reject file with reason codes
- Processing continues with next transaction after error
- Statistics displayed at end of processing

## Related Programs
- **CBTRN02C**: Main batch posting program
- **1500-VALIDATE-TRAN**: Transaction validation routine
- **2000-POST-TRANSACTION**: Transaction posting routine
- **2500-WRITE-REJECT-REC**: Reject file writing routine
