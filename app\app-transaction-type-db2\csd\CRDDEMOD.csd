DEFINE MAPSET(COTRTLI) GROUP(CARDDEMO)
DESCRIPTION(CREDIT CARD TRAN TYPE INQ MAP)
       RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
       DEFINETIME(23/03/09 18:09:14) CHANGETIME(23/03/09 18:09:14)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE MAPSET(COTRTUP) GROUP(CARDDEMO)
DESCRIPTION(CREDIT CARD TRAN TYPE MAINT MAP)
       RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
       DEFINETIME(23/03/09 18:09:26) CHANGETIME(23/03/09 18:09:26)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE PROGRAM(COTRTLIC) GROUP(CARDDEMO)
       LANGUAGE(COBOL) RELOAD(YES) RESIDENT(NO) USAGE(NORMAL)
       USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
       EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
       TRANSID(CTLI) EXECUTIONSET(FULLAPI) JVM(NO)
       DEFINETIME(23/03/06 23:47:41) CHANGETIME(23/03/06 23:49:29)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE PROGRAM(COTRTUPC) GROUP(CARDDEMO)
       LANGUAGE(COBOL) RELOAD(YES) RESIDENT(NO) USAGE(NORMAL)
       USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
       EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
       TRANSID(CTTU) EXECUTIONSET(FULLAPI) JVM(NO)
       DEFINETIME(23/03/09 17:34:50) CHANGETIME(23/03/09 17:35:11)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE TRANSACTION(CTLI) GROUP(CARDDEMO)
       PROGRAM(COTRTLIC) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
       TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
       RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
       ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
       RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
       CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
       WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
       DEFINETIME(23/03/13 15:32:24) CHANGETIME(23/03/13 15:32:51)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE TRANSACTION(CTTU) GROUP(CARDDEMO)
       PROGRAM(COTRTUPC) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
       TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
       RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
       ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
       RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
       CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
       WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
       DEFINETIME(23/03/09 17:34:06) CHANGETIME(23/03/09 17:34:39)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE DB2ENTRY(CARDDEMO) GROUP(CARDDEMO)
DESCRIPTION(DB2 RETRY FOR CARDDEMO PLAN)
       ACCOUNTREC(TXID) AUTHTYPE(USERID) DROLLBACK(YES) PLAN(CARDDEMO)
       PRIORITY(HIGH) PROTECTNUM(0) THREADLIMIT(1) THREADWAIT(YES)     
       DEFINETIME(22/11/27 19:11:50) CHANGETIME(22/11/27 19:12:39)     
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)      
DEFINE DB2TRAN(CTLITRAN) GROUP(CARDDEMO)
DESCRIPTION(DB2TRAN FOR CTLI TRANSACTION)
       ENTRY(CARDDEMO) TRANSID(CTLI) DEFINETIME(23/03/24 11:16:32)
       CHANGETIME(23/03/24 11:16:53) CHANGEUSRID(AWSUSER)              
       CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE DB2TRAN(CTTUTRAN) GROUP(CARDDEMO)
DESCRIPTION(DB2TRAN FOR CTTU TRANSACTION)
       ENTRY(CARDDEMO) TRANSID(CTTU) DEFINETIME(23/03/24 11:16:32)
       CHANGETIME(23/03/24 11:16:53) CHANGEUSRID(AWSUSER)
       CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)