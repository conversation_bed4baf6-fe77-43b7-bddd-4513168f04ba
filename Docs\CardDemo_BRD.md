# Business Requirements Document (BRD)
## COBOL Credit Card Processing System (CardDemo)

### Document Information
- **Document Version**: 1.0
- **Date**: 2025-08-22
- **System**: CardDemo Credit Card Processing System
- **Technology**: COBOL/CICS/VSAM/DB2

---

## 1. Executive Summary

The CardDemo system is a comprehensive COBOL-based credit card processing platform that manages the complete lifecycle of credit card operations including customer management, account administration, card issuance, transaction processing, authorization, billing, and reporting. The system supports both online CICS transactions and batch processing workflows to handle high-volume credit card operations.

### 1.1 System Overview
- **Primary Function**: Credit card transaction processing and account management
- **Architecture**: Mainframe COBOL with CICS online processing and batch components
- **Data Storage**: VSAM files with DB2 integration for reference data
- **Integration**: MQ messaging for authorization and fraud detection

---

## 2. Business Objectives

### 2.1 Primary Objectives
- **Transaction Processing**: Process credit card transactions with real-time authorization
- **Account Management**: Maintain customer accounts, credit limits, and balances
- **Risk Management**: Implement fraud detection and credit limit controls
- **Regulatory Compliance**: Ensure audit trails and compliance reporting
- **Customer Service**: Provide comprehensive account and transaction management

### 2.2 Success Criteria
- 99.9% transaction processing availability
- Sub-second response times for authorization requests
- Zero tolerance for financial calculation errors
- Complete audit trail for all transactions
- Regulatory compliance for financial reporting

---

## 3. Functional Requirements

### 3.1 Customer Management (Domain: Customer Management)

#### 3.1.1 Customer Registration and Maintenance
- **FR-001**: System shall maintain customer records with complete demographic information
- **FR-002**: System shall validate customer SSN and government-issued ID
- **FR-003**: System shall track customer FICO credit scores
- **FR-004**: System shall support multiple phone numbers and addresses per customer
- **FR-005**: System shall maintain customer EFT account information

#### 3.1.2 Customer Data Structure
```
Customer Record (500 bytes):
- Customer ID (9 digits)
- Name (First, Middle, Last - 75 chars total)
- Address (3 lines, state, country, zip - 162 chars)
- Phone numbers (2 numbers - 30 chars)
- SSN (9 digits)
- Government ID (20 chars)
- Date of Birth
- EFT Account ID
- Primary cardholder indicator
- FICO credit score (3 digits)
```

### 3.2 Account Management (Domain: Account Management)

#### 3.2.1 Account Creation and Maintenance
- **FR-006**: System shall create and maintain credit card accounts
- **FR-007**: System shall enforce credit limits and cash advance limits
- **FR-008**: System shall track account balances and cycle credits/debits
- **FR-009**: System shall manage account status (Active/Inactive)
- **FR-010**: System shall maintain account open, expiration, and reissue dates

#### 3.2.2 Account Data Structure
```
Account Record (300 bytes):
- Account ID (11 digits)
- Active Status (Y/N)
- Current Balance (signed, 2 decimals)
- Credit Limit (signed, 2 decimals)
- Cash Credit Limit (signed, 2 decimals)
- Open Date, Expiration Date, Reissue Date
- Current Cycle Credit/Debit amounts
- Address ZIP code
```

### 3.3 Card Management (Domain: Account Management)

#### 3.3.1 Card Issuance and Maintenance
- **FR-011**: System shall issue credit cards linked to accounts
- **FR-012**: System shall generate and validate CVV codes
- **FR-013**: System shall manage card expiration dates and reissuance
- **FR-014**: System shall maintain card status (Active/Inactive)
- **FR-015**: System shall support card updates and replacements

#### 3.3.2 Card Data Structure
```
Card Record (150 bytes):
- Card Number (16 digits)
- Account ID (11 digits)
- CVV Code (3 digits)
- Embossed Name (50 chars)
- Expiration Date
- Active Status
```

### 3.4 Transaction Processing (Domain: Transaction Processing)

#### 3.4.1 Transaction Types
- **FR-016**: System shall support the following transaction types:
  - 01: PURCHASE
  - 02: PAYMENT
  - 03: CREDIT
  - 04: AUTHORIZATION
  - 05: REFUND
  - 06: REVERSAL
  - 07: ADJUSTMENT

#### 3.4.2 Transaction Processing Workflow
- **FR-017**: System shall validate all incoming transactions
- **FR-018**: System shall perform real-time authorization checks
- **FR-019**: System shall update account balances upon transaction posting
- **FR-020**: System shall maintain complete transaction audit trail

#### 3.4.3 Transaction Data Structure
```
Transaction Record (350 bytes):
- Transaction ID (16 chars)
- Transaction Type Code (2 chars)
- Category Code (4 digits)
- Source (10 chars)
- Description (100 chars)
- Amount (signed, 2 decimals)
- Card Number (16 digits)
- Merchant Information (ID, Name, City, ZIP)
- Timestamps (Original, Processing)
```

### 3.5 Authorization Processing (Domain: Risk Management)

#### 3.5.1 Real-time Authorization
- **FR-021**: System shall perform real-time credit limit validation
- **FR-022**: System shall check account status before authorization
- **FR-023**: System shall validate card expiration dates
- **FR-024**: System shall implement fraud detection rules
- **FR-025**: System shall log all authorization attempts

### 3.6 Payment Processing (Domain: Payment Processing)

#### 3.6.1 Bill Payment Processing
- **FR-026**: System shall process online bill payments
- **FR-027**: System shall validate payment amounts against account balances
- **FR-028**: System shall generate payment confirmation
- **FR-029**: System shall update account balances for payments

### 3.7 Batch Processing (Domain: Transaction Processing)

#### 3.7.1 Daily Transaction Processing
- **FR-030**: System shall process daily transaction files in batch
- **FR-031**: System shall validate transactions before posting
- **FR-032**: System shall reject invalid transactions with reason codes
- **FR-033**: System shall update account balances and transaction history

#### 3.7.2 Interest and Fee Calculation
- **FR-034**: System shall calculate interest on outstanding balances
- **FR-035**: System shall apply fees according to account terms
- **FR-036**: System shall generate billing statements

### 3.8 Reporting and Reconciliation (Domain: Reporting and Reconciliation)

#### 3.8.1 Transaction Reporting
- **FR-037**: System shall generate transaction reports by date range
- **FR-038**: System shall provide account balance reports
- **FR-039**: System shall generate exception and reject reports
- **FR-040**: System shall support regulatory compliance reporting

---

## 4. Business Rules Catalog

### 4.1 Account Management Rules
- **BR-001**: Account ID must be unique 11-digit number
- **BR-002**: Credit limit must be greater than zero
- **BR-003**: Account status must be 'Y' (Active) or 'N' (Inactive)
- **BR-004**: Account expiration date must be future date
- **BR-005**: Cash credit limit cannot exceed regular credit limit

### 4.2 Card Management Rules
- **BR-006**: Card number must be unique 16-digit number
- **BR-007**: CVV must be 3-digit numeric code
- **BR-008**: Card expiration date must be future date
- **BR-009**: Card must be linked to valid active account
- **BR-010**: Card status must be 'Y' (Active) or 'N' (Inactive)

### 4.3 Transaction Processing Rules
- **BR-011**: Transaction amount cannot exceed available credit
- **BR-012**: Transaction cannot be processed on expired account
- **BR-013**: Card number must exist in card cross-reference file
- **BR-014**: Account must exist and be active for transaction processing
- **BR-015**: Transaction ID must be unique

### 4.4 Authorization Rules
- **BR-016**: Available credit = Credit Limit - Current Balance - Pending Authorizations
- **BR-017**: Transaction declined if amount > available credit
- **BR-018**: Transaction declined if account is inactive
- **BR-019**: Transaction declined if card is expired
- **BR-020**: All authorization attempts must be logged

### 4.5 Payment Processing Rules
- **BR-021**: Payment amount must be positive
- **BR-022**: Payment cannot exceed current account balance
- **BR-023**: Payment must be applied to oldest charges first
- **BR-024**: Payment confirmation must be generated

### 4.6 Data Validation Rules
- **BR-025**: Customer SSN must be 9-digit numeric
- **BR-026**: Phone numbers must be valid format
- **BR-027**: ZIP codes must be valid format
- **BR-028**: Dates must be in YYYY-MM-DD format
- **BR-029**: All monetary amounts must have 2 decimal places

---

## 5. Domain Model and Entity Relationships

### 5.1 Core Entities

#### 5.1.1 Customer Entity
- **Primary Key**: Customer ID (9 digits)
- **Attributes**: Name, Address, Phone, SSN, DOB, FICO Score
- **Relationships**: One-to-Many with Accounts

#### 5.1.2 Account Entity
- **Primary Key**: Account ID (11 digits)
- **Attributes**: Balance, Credit Limits, Status, Dates
- **Relationships**: Many-to-One with Customer, One-to-Many with Cards

#### 5.1.3 Card Entity
- **Primary Key**: Card Number (16 digits)
- **Attributes**: CVV, Expiration, Status, Embossed Name
- **Relationships**: Many-to-One with Account

#### 5.1.4 Transaction Entity
- **Primary Key**: Transaction ID (16 characters)
- **Attributes**: Type, Amount, Merchant Info, Timestamps
- **Relationships**: Many-to-One with Card/Account

#### 5.1.5 Card Cross-Reference Entity
- **Primary Key**: Card Number (16 digits)
- **Attributes**: Account ID, Customer ID
- **Purpose**: Links cards to accounts for transaction processing

### 5.2 Entity Relationship Diagram
```
Customer (1) ----< Account (1) ----< Card (1) ----< Transaction
    |                |                |                |
CUST-ID          ACCT-ID         CARD-NUM         TRAN-ID
                     |                |                |
                     +--------< Card-XRef >-----------+
```

---

## 6. Business Process Flows

### 6.1 Account Opening Process
1. Customer information validation
2. Credit check and FICO score verification
3. Account creation with credit limit assignment
4. Card issuance and activation
5. Welcome package generation

### 6.2 Transaction Authorization Process
1. Transaction request received
2. Card validation in cross-reference file
3. Account lookup and status check
4. Credit limit validation
5. Fraud detection screening
6. Authorization response generation
7. Transaction logging

### 6.3 Daily Transaction Processing
1. Daily transaction file receipt
2. Transaction validation and editing
3. Account balance updates
4. Transaction posting to history
5. Exception and reject reporting
6. Reconciliation and balancing

### 6.4 Billing and Payment Process
1. Interest and fee calculation
2. Statement generation
3. Payment processing
4. Balance updates
5. Payment confirmation

---

## 7. Data Requirements

### 7.1 File Structures
- **CUSTDATA**: Customer master file (VSAM KSDS, 500-byte records)
- **ACCTDATA**: Account master file (VSAM KSDS, 300-byte records)
- **CARDDATA**: Card master file (VSAM KSDS, 150-byte records)
- **CARDXREF**: Card cross-reference file (VSAM KSDS)
- **TRANSACT**: Transaction history file (VSAM KSDS, 350-byte records)
- **TCATBALF**: Transaction category balance file (VSAM KSDS)

### 7.2 Data Retention
- Transaction data: 7 years
- Account data: Life of account + 7 years
- Customer data: Life of relationship + 7 years
- Authorization logs: 2 years

---

## 8. Integration Requirements

### 8.1 External Interfaces
- **Authorization Network**: Real-time transaction authorization
- **Fraud Detection System**: Risk scoring and monitoring
- **Credit Bureau Interface**: FICO score updates
- **Payment Networks**: Visa/MasterCard processing
- **Regulatory Reporting**: Compliance data feeds

### 8.2 Internal Interfaces
- **CICS Online System**: Real-time transaction processing
- **Batch Processing**: Daily file processing
- **DB2 Database**: Reference data management
- **MQ Messaging**: Asynchronous communication
- **Report Generation**: Business intelligence feeds

---

This BRD provides a comprehensive overview of the CardDemo credit card processing system's business requirements, extracted from the COBOL codebase analysis. The document serves as a foundation for understanding the system's business logic, data structures, and operational processes.
