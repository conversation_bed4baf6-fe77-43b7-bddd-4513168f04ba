//COMPILEC JOB 'M2APP',CLASS=A,MSGCLASS=0,MSGLEVEL=(1,1),               
//             REGION=0M,NOTIFY=&SYSUID,TIME=1440                      
//*  ---------------------------                                        
//*  BATCH COMPILER JCL 
//*  ---------------------------                                        
//*         
//BLDBAT PROC                                                                   
//COMPILE EXEC PGM=IGYCRCTL,REGION=0M,                                  
//   PARM=(APOST,LIST,MAP,NUMBER)                                       
//STEPLIB  DD DISP=SHR,                                                        
//         DSN=IGY.SIGYCOMP.V63 
//SYSIN    DD DISP=SHR,                                                 
//         DSN=AWS.M2.CARDDEMO.CBL(&MEM)                                
//SYSLIB   DD DISP=SHR,                                                 
//         DSN=AWS.M2.CARDDEMO.CPY                                      
//SYSPRINT DD DISP=SHR,                                                 
//         DSN=AWS.M2.CARDDEMO.LISTING(&MEM)                            
//SYSLIN   DD DSN=&&LOADSET,UNIT=3390,                                  
//            DISP=(MOD,PASS),SPACE=(80,(10,10))                        
//SYSUT1   DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSUT2   DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSUT3   DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSUT4   DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSUT5   DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSUT6   DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSUT7   DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSUT8   DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSUT9   DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSUT10  DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSUT11  DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSUT12  DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSUT13  DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSUT14  DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSUT15  DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//SYSMDECK DD SPACE=(80,(10,10),,,ROUND),UNIT=3390                      
//*                                                                     
//*  *********************************                                  
//*        COPY THE LISTING TO SYSOUT                                   
//*  *********************************                                  
//CBLPRINT  EXEC PGM=IEBGENER,REGION=0M                                 
//SYSPRINT  DD SYSOUT=*                                                 
//SYSUT1    DD DISP=SHR,                                                
//          DSN=AWS.M2.CARDDEMO.LISTING(&MEM)                           
//SYSUT2    DD SYSOUT=*                                                 
//SYSIN     DD DUMMY                                                    
//*                                                                     
//*  *********************************                                  
//*        LINK-EDIT (BINDER) STEP                                      
//*  *********************************                                  
//*                                                                     
//LKED EXEC PGM=HEWL,REGION=0M,PARM='LIST,XREF'                         
//SYSPRINT DD SYSOUT=*                                                  
//SYSLIB   DD DSN=SYS1.LINKLIB,DISP=SHR                                 
//         DD DSN=CSF.SCSFMOD0,DISP=SHR                                 
//         DD DSN=CEE.SCEELKED,DISP=SHR                                 
//         DD DSN=CEE.SCEELKEX,DISP=SHR                                 
//         DD DSN=AWS.M2.CARDDEMO.LOADLIB,DISP=SHR                      
//SYSLIN   DD DSN=&&LOADSET,DISP=(OLD,DELETE)                           
//SYSLMOD  DD DISP=SHR,                                                 
//         DSN=AWS.M2.CARDDEMO.LOADLIB(&MEM)                            
//SYSUT1   DD UNIT=3390,DCB=BLKSIZE=1024,SPACE=(1024,(200,20))          
// PEND                                                                 
//*                                                                     
//STEP1 EXEC BLDBAT,MEM=ZZZZZZZZ                                        
