CREATE TABLE CARDDEMO.AUTHFRDS
(CARD_NUM              CHAR(16)    NOT NULL,
    AUTH_TS                TIMESTAMP   NOT NULL,
    AUTH_TYPE              CHAR(4)             ,
    CARD_EXPIRY_DATE       CHAR(4)             ,
    MESSAGE_TYPE           CHAR(6)             ,
    MESSAGE_SOURCE         CHAR(6)             ,
    AUTH_ID_CODE           CHAR(6)             ,
    AUTH_RESP_CODE         CHAR(2)             ,
    AUTH_RESP_REASON       CHAR(4)             ,
    PROCESSING_CODE        CHAR(6)             ,
    TRANSACTION_AMT        DECIMAL(12,2)       ,
    APPROVED_AMT           DECIMAL(12,2)       ,
    MERCHANT_CATAGORY_CODE CHAR(4)             ,
    ACQR_COUNTRY_CODE      CHAR(3)             ,
    POS_ENTRY_MODE         SMALLINT            ,
    MERCHANT_ID            CHAR(15)            ,
    MERCHANT_NAME          VARCHAR(22)         ,
    MERCHANT_CITY          CHAR(13)            ,
    MERCHANT_STATE         CHAR(02)            ,
    MERCHANT_ZIP           CHAR(09)            ,
    T<PERSON><PERSON>ACTION_ID         CHAR(15)            ,
    MATCH_STATUS           CHAR(1)             ,
    AUTH_FRAUD             CHAR(1)             ,
    FRAUD_RPT_DATE         DATE                ,
    ACCT_ID                DECIMAL(11)         ,
    CUST_ID                DECIMAL(9)          ,
    PRIMARY KEY(CARD_NUM,AUTH_TS )             );
