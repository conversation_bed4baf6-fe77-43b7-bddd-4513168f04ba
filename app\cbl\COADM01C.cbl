      ******************************************************************        
      * Program     : COADM01C.CBL                                              
      * Application : CardDemo                                                  
      * Type        : CICS COBOL Program                                        
      * Function    : Admin Menu for Admin users                                
      ******************************************************************        
      * Copyright Amazon.com, Inc. or its affiliates.                           
      * All Rights Reserved.                                                    
      *                                                                         
      * Licensed under the Apache License, Version 2.0 (the "License").         
      * You may not use this file except in compliance with the License.        
      * You may obtain a copy of the License at                                 
      *                                                                         
      *    http://www.apache.org/licenses/LICENSE-2.0                           
      *                                                                         
      * Unless required by applicable law or agreed to in writing,              
      * software distributed under the License is distributed on an             
      * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,            
      * either express or implied. See the License for the specific             
      * language governing permissions and limitations under the License        
      ******************************************************************        
       IDENTIFICATION DIVISION.                                                 
       PROGRAM-ID. COADM01C.                                                    
       AUTHOR.     AWS.                                                         
                                                                                
       ENVIRONMENT DIVISION.                                                    
       CONFIGURATION SECTION.                                                   
                                                                                
       DATA DIVISION.                                                           
      *----------------------------------------------------------------*        
      *                     WORKING STORAGE SECTION                             
      *----------------------------------------------------------------*        
       WORKING-STORAGE SECTION.                                                 
                                                                                
       01 WS-VARIABLES.                                                         
         05 WS-PGMNAME                 PIC X(08) VALUE 'COADM01C'.              
         05 WS-TRANID                  PIC X(04) VALUE 'CA00'.                  
         05 WS-MESSAGE                 PIC X(80) VALUE SPACES.                  
         05 WS-USRSEC-FILE             PIC X(08) VALUE 'USRSEC  '.              
         05 WS-ERR-FLG                 PIC X(01) VALUE 'N'.                     
           88 ERR-FLG-ON                         VALUE 'Y'.                     
           88 ERR-FLG-OFF                        VALUE 'N'.                     
         05 WS-RESP-CD                 PIC S9(09) COMP VALUE ZEROS.             
         05 WS-REAS-CD                 PIC S9(09) COMP VALUE ZEROS.             
         05 WS-OPTION-X                PIC X(02) JUST RIGHT.                    
         05 WS-OPTION                  PIC 9(02) VALUE 0.                       
         05 WS-IDX                     PIC S9(04) COMP VALUE ZEROS.             
         05 WS-ADMIN-OPT-TXT           PIC X(40) VALUE SPACES.                  
                                                                                
       COPY COCOM01Y.                                                           
       COPY COADM02Y.                                                           
                                                                                
       COPY COADM01.                                                            
                                                                                
       COPY COTTL01Y.                                                           
       COPY CSDAT01Y.                                                           
       COPY CSMSG01Y.                                                           
       COPY CSUSR01Y.                                                           
                                                                                
       COPY DFHAID.                                                             
       COPY DFHBMSCA.                                                           
                                                                                
      *----------------------------------------------------------------*        
      *                        LINKAGE SECTION                                  
      *----------------------------------------------------------------*        
       LINKAGE SECTION.                                                         
       01  DFHCOMMAREA.                                                         
         05  LK-COMMAREA                           PIC X(01)                    
             OCCURS 1 TO 32767 TIMES DEPENDING ON EIBCALEN.                     
                                                                                
      *----------------------------------------------------------------*        
      *                       PROCEDURE DIVISION                                
      *----------------------------------------------------------------*        
       PROCEDURE DIVISION.                                                      
       MAIN-PARA.                                                               
                                                                                
           EXEC CICS
                HANDLE CONDITION PGMIDERR(PGMIDERR-ERR-PARA)
           END-EXEC

           SET ERR-FLG-OFF TO TRUE                                              
                                                                                
           MOVE SPACES TO WS-MESSAGE                                            
                          ERRMSGO OF COADM1AO                                   
                                                                                
           IF EIBCALEN = 0                                                      
               MOVE 'COSGN00C' TO CDEMO-FROM-PROGRAM                            
               PERFORM RETURN-TO-SIGNON-SCREEN                                  
           ELSE                                                                 
               MOVE DFHCOMMAREA(1:EIBCALEN) TO CARDDEMO-COMMAREA                
               IF NOT CDEMO-PGM-REENTER                                         
                   SET CDEMO-PGM-REENTER    TO TRUE                             
                   MOVE LOW-VALUES          TO COADM1AO                         
                   PERFORM SEND-MENU-SCREEN                                     
               ELSE                                                             
                   PERFORM RECEIVE-MENU-SCREEN                                  
                   EVALUATE EIBAID                                              
                       WHEN DFHENTER                                            
                           PERFORM PROCESS-ENTER-KEY                            
                       WHEN DFHPF3                                              
                           MOVE 'COSGN00C' TO CDEMO-TO-PROGRAM                  
                           PERFORM RETURN-TO-SIGNON-SCREEN                      
                       WHEN OTHER                                               
                           MOVE 'Y'                       TO WS-ERR-FLG         
                           MOVE CCDA-MSG-INVALID-KEY      TO WS-MESSAGE         
                           PERFORM SEND-MENU-SCREEN                             
                   END-EVALUATE                                                 
               END-IF                                                           
           END-IF                                                               
                                                                                
           EXEC CICS RETURN                                                     
                     TRANSID (WS-TRANID)                                        
                     COMMAREA (CARDDEMO-COMMAREA)                               
           END-EXEC.                                                            
                                                                                
      *----------------------------------------------------------------*        
      *                      PROCESS-ENTER-KEY                                  
      *----------------------------------------------------------------*        
       PROCESS-ENTER-KEY.                                                       
                                                                                
           PERFORM VARYING WS-IDX                                               
                   FROM LENGTH OF OPTIONI OF COADM1AI BY -1 UNTIL               
                   OPTIONI OF COADM1AI(WS-IDX:1) NOT = SPACES OR                
                   WS-IDX = 1                                                   
           END-PERFORM                                                          
           MOVE OPTIONI OF COADM1AI(1:WS-IDX) TO WS-OPTION-X                    
           INSPECT WS-OPTION-X REPLACING ALL ' ' BY '0'                         
           MOVE WS-OPTION-X              TO WS-OPTION                           
           MOVE WS-OPTION                TO OPTIONO OF COADM1AO                 
                                                                                
           IF WS-OPTION IS NOT NUMERIC OR                                       
              WS-OPTION > CDEMO-ADMIN-OPT-COUNT OR                              
              WS-OPTION = ZEROS                                                 
               MOVE 'Y'     TO WS-ERR-FLG                                       
               MOVE 'Please enter a valid option number...' TO                  
                                       WS-MESSAGE                               
               PERFORM SEND-MENU-SCREEN                                         
           END-IF                                                               

           IF NOT ERR-FLG-ON                                                    
               IF CDEMO-ADMIN-OPT-PGMNAME(WS-OPTION)(1:5) NOT = 'DUMMY'
                   MOVE WS-TRANID    TO CDEMO-FROM-TRANID                       
                   MOVE WS-PGMNAME   TO CDEMO-FROM-PROGRAM                      
                   MOVE ZEROS        TO CDEMO-PGM-CONTEXT                       
                   EXEC CICS                                                    
                       XCTL PROGRAM(CDEMO-ADMIN-OPT-PGMNAME(WS-OPTION))         
                       COMMAREA(CARDDEMO-COMMAREA)                              
                   END-EXEC                                                     
               END-IF                                                           
               MOVE SPACES             TO WS-MESSAGE                            
               MOVE DFHGREEN           TO ERRMSGC  OF COADM1AO                  
               STRING 'This option '       DELIMITED BY SIZE                    
      *                CDEMO-ADMIN-OPT-NAME(WS-OPTION)                          
      *                                DELIMITED BY SIZE                        
                       'is not installed ...'   DELIMITED BY SIZE               
                  INTO WS-MESSAGE                                               
               PERFORM SEND-MENU-SCREEN                                         
           END-IF.                                                              
                                                                                
      *----------------------------------------------------------------*        
      *                      RETURN-TO-SIGNON-SCREEN                            
      *----------------------------------------------------------------*        
       RETURN-TO-SIGNON-SCREEN.                                                 
                                                                                
           IF CDEMO-TO-PROGRAM = LOW-VALUES OR SPACES                           
               MOVE 'COSGN00C' TO CDEMO-TO-PROGRAM                              
           END-IF                                                               
           EXEC CICS                                                            
               XCTL PROGRAM(CDEMO-TO-PROGRAM)                                   
           END-EXEC.                                                            
                                                                                
      *----------------------------------------------------------------*        
      *                      SEND-MENU-SCREEN                                   
      *----------------------------------------------------------------*        
       SEND-MENU-SCREEN.                                                        
                                                                                
           PERFORM POPULATE-HEADER-INFO                                         
           PERFORM BUILD-MENU-OPTIONS                                           
                                                                                
           MOVE WS-MESSAGE TO ERRMSGO OF COADM1AO                               
                                                                                
           EXEC CICS SEND                                                       
                     MAP('COADM1A')                                             
                     MAPSET('COADM01')                                          
                     FROM(COADM1AO)                                             
                     ERASE                                                      
           END-EXEC.                                                            
                                                                                
      *----------------------------------------------------------------*        
      *                      RECEIVE-MENU-SCREEN                                
      *----------------------------------------------------------------*        
       RECEIVE-MENU-SCREEN.                                                     
                                                                                
           EXEC CICS RECEIVE                                                    
                     MAP('COADM1A')                                             
                     MAPSET('COADM01')                                          
                     INTO(COADM1AI)                                             
                     RESP(WS-RESP-CD)                                           
                     RESP2(WS-REAS-CD)                                          
           END-EXEC.                                                            
                                                                                
      *----------------------------------------------------------------*        
      *                      POPULATE-HEADER-INFO                               
      *----------------------------------------------------------------*        
       POPULATE-HEADER-INFO.                                                    
                                                                                
           MOVE FUNCTION CURRENT-DATE  TO WS-CURDATE-DATA                       
                                                                                
           MOVE CCDA-TITLE01           TO TITLE01O OF COADM1AO                  
           MOVE CCDA-TITLE02           TO TITLE02O OF COADM1AO                  
           MOVE WS-TRANID              TO TRNNAMEO OF COADM1AO                  
           MOVE WS-PGMNAME             TO PGMNAMEO OF COADM1AO                  
                                                                                
           MOVE WS-CURDATE-MONTH       TO WS-CURDATE-MM                         
           MOVE WS-CURDATE-DAY         TO WS-CURDATE-DD                         
           MOVE WS-CURDATE-YEAR(3:2)   TO WS-CURDATE-YY                         
                                                                                
           MOVE WS-CURDATE-MM-DD-YY    TO CURDATEO OF COADM1AO                  
                                                                                
           MOVE WS-CURTIME-HOURS       TO WS-CURTIME-HH                         
           MOVE WS-CURTIME-MINUTE      TO WS-CURTIME-MM                         
           MOVE WS-CURTIME-SECOND      TO WS-CURTIME-SS                         
                                                                                
           MOVE WS-CURTIME-HH-MM-SS    TO CURTIMEO OF COADM1AO.                 
                                                                                
      *----------------------------------------------------------------*        
      *                      BUILD-MENU-OPTIONS                                 
      *----------------------------------------------------------------*        
       BUILD-MENU-OPTIONS.                                                      
                                                                                
           PERFORM VARYING WS-IDX FROM 1 BY 1 UNTIL                             
                           WS-IDX > CDEMO-ADMIN-OPT-COUNT                       
                                                                                
               MOVE SPACES             TO WS-ADMIN-OPT-TXT                      
                                                                                
               STRING CDEMO-ADMIN-OPT-NUM(WS-IDX)  DELIMITED BY SIZE            
                      '. '                         DELIMITED BY SIZE            
                      CDEMO-ADMIN-OPT-NAME(WS-IDX) DELIMITED BY SIZE            
                 INTO WS-ADMIN-OPT-TXT                                          
                                                                                
               EVALUATE WS-IDX                                                  
                   WHEN 1                                                       
                       MOVE WS-ADMIN-OPT-TXT TO OPTN001O                        
                   WHEN 2                                                       
                       MOVE WS-ADMIN-OPT-TXT TO OPTN002O                        
                   WHEN 3                                                       
                       MOVE WS-ADMIN-OPT-TXT TO OPTN003O                        
                   WHEN 4                                                       
                       MOVE WS-ADMIN-OPT-TXT TO OPTN004O                        
                   WHEN 5                                                       
                       MOVE WS-ADMIN-OPT-TXT TO OPTN005O                        
                   WHEN 6                                                       
                       MOVE WS-ADMIN-OPT-TXT TO OPTN006O                        
                   WHEN 7                                                       
                       MOVE WS-ADMIN-OPT-TXT TO OPTN007O                        
                   WHEN 8                                                       
                       MOVE WS-ADMIN-OPT-TXT TO OPTN008O                        
                   WHEN 9                                                       
                       MOVE WS-ADMIN-OPT-TXT TO OPTN009O                        
                   WHEN 10                                                      
                       MOVE WS-ADMIN-OPT-TXT TO OPTN010O                        
                   WHEN OTHER                                                   
                       CONTINUE                                                 
               END-EVALUATE                                                     
                                                                                
           END-PERFORM.                                                         
      *----------------------------------------------------------------*        
      *      PGMIDERROR     HANDLE-MISSING MENU OPTIONS                         
      *----------------------------------------------------------------*        
       PGMIDERR-ERR-PARA.
           MOVE SPACES             TO WS-MESSAGE                                
           MOVE DFHGREEN           TO ERRMSGC  OF COADM1AO                      
           STRING 'This option '       DELIMITED BY SIZE                        
      *                CDEMO-ADMIN-OPT-NAME(WS-OPTION)                          
      *                                DELIMITED BY SIZE                        
                       'is not installed ...'   DELIMITED BY SIZE               
           INTO WS-MESSAGE
                                                                                
           PERFORM SEND-MENU-SCREEN
           EXEC CICS RETURN                                                     
                     TRANSID (WS-TRANID)                                        
                     COMMAREA (CARDDEMO-COMMAREA)                               
           END-EXEC.
           .
                                                                                
      *                                                                         
      * Ver: CardDemo_v1.0-15-g27d6c6f-68 Date: 2022-07-19 23:12:32 CDT         
      *                                                                         
