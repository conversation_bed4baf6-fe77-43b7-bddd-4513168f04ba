# Domain Entity Model - Credit Card Processing System

## Overview
This diagram shows the complete domain model with all entities, their relationships, and key attributes for the CardDemo credit card processing system.

## Business Domain
The system manages the complete credit card lifecycle including customer onboarding, account management, card issuance, transaction processing, and billing.

## Mermaid Diagram Code

```mermaid
erDiagram
    CUSTOMER {
        int CUST_ID PK "9 digits, unique"
        string CUST_FIRST_NAME "25 chars, required"
        string CUST_MIDDLE_NAME "25 chars, optional"
        string CUST_LAST_NAME "25 chars, required"
        string CUST_ADDR_LINE_1 "50 chars, required"
        string CUST_ADDR_LINE_2 "50 chars, optional"
        string CUST_ADDR_LINE_3 "50 chars, city"
        string CUST_ADDR_STATE_CD "2 chars, US state"
        string CUST_ADDR_COUNTRY_CD "3 chars"
        string CUST_ADDR_ZIP "10 chars, numeric"
        string CUST_PHONE_NUM_1 "15 chars, required"
        string CUST_PHONE_NUM_2 "15 chars, optional"
        int CUST_SSN "9 digits, unique"
        string CUST_GOVT_ISSUED_ID "20 chars"
        date CUST_DOB_YYYYMMDD "Date of birth"
        string CUST_EFT_ACCOUNT_ID "10 chars"
        string CUST_PRI_CARD_HOLDER_IND "Y/N"
        int CUST_FICO_CREDIT_SCORE "300-850"
    }
    
    ACCOUNT {
        int ACCT_ID PK "11 digits, unique"
        string ACCT_ACTIVE_STATUS "Y/N"
        decimal ACCT_CURR_BAL "Current balance"
        decimal ACCT_CREDIT_LIMIT "Credit limit"
        decimal ACCT_CASH_CREDIT_LIMIT "Cash advance limit"
        date ACCT_OPEN_DATE "Account open date"
        date ACCT_EXPIRAION_DATE "Account expiration"
        date ACCT_REISSUE_DATE "Card reissue date"
        decimal ACCT_CURR_CYC_CREDIT "Cycle credits"
        decimal ACCT_CURR_CYC_DEBIT "Cycle debits"
        string ACCT_ADDR_ZIP "Account ZIP"
        string ACCT_GROUP_ID "Account group"
    }
    
    CARD {
        string CARD_NUM PK "16 digits, unique"
        int CARD_ACCT_ID FK "Links to account"
        int CARD_CVV_CD "3 digits"
        string CARD_EMBOSSED_NAME "50 chars"
        date CARD_EXPIRAION_DATE "Card expiration"
        string CARD_ACTIVE_STATUS "Y/N"
    }
    
    CARD_XREF {
        string XREF_CARD_NUM PK "16 digits"
        int XREF_CUST_ID FK "Links to customer"
        int XREF_ACCT_ID FK "Links to account"
    }
    
    TRANSACTION {
        string TRAN_ID PK "16 chars, unique"
        string TRAN_TYPE_CD "Transaction type"
        int TRAN_CAT_CD "Category code"
        string TRAN_SOURCE "Transaction source"
        string TRAN_DESC "Description"
        decimal TRAN_AMT "Transaction amount"
        int TRAN_MERCHANT_ID "Merchant ID"
        string TRAN_MERCHANT_NAME "Merchant name"
        string TRAN_MERCHANT_CITY "Merchant city"
        string TRAN_MERCHANT_ZIP "Merchant ZIP"
        string TRAN_CARD_NUM FK "Card used"
        timestamp TRAN_ORIG_TS "Original timestamp"
        timestamp TRAN_PROC_TS "Processing timestamp"
    }
    
    TRANSACTION_TYPE {
        string TR_TYPE PK "2 chars"
        string TR_DESCRIPTION "Type description"
    }
    
    TRANSACTION_CATEGORY_BALANCE {
        int TRANCAT_ACCT_ID PK "Account ID"
        string TRANCAT_TYPE_CD PK "Transaction type"
        int TRANCAT_CD PK "Category code"
        decimal TRAN_CAT_BAL "Category balance"
    }
    
    DISCLOSURE_GROUP {
        string DIS_ACCT_GROUP_ID PK "Account group"
        string DIS_TRAN_TYPE_CD PK "Transaction type"
        int DIS_TRAN_CAT_CD PK "Category code"
        decimal DIS_INT_RATE "Interest rate"
        string DIS_INT_RATE_TYPE "Rate type"
        decimal DIS_FEES "Associated fees"
    }
    
    USER_SECURITY {
        string USER_ID PK "User identifier"
        string USER_NAME "User name"
        string USER_PASSWORD "Encrypted password"
        string USER_TYPE "User type/role"
        string USER_FIRST_NAME "First name"
        string USER_LAST_NAME "Last name"
        date USER_LAST_SIGNON "Last signon date"
        int USER_SIGNON_COUNT "Signon count"
    }

    %% Relationships
    CUSTOMER ||--o{ ACCOUNT : "owns"
    ACCOUNT ||--o{ CARD : "has"
    CARD ||--|| CARD_XREF : "references"
    CUSTOMER ||--|| CARD_XREF : "linked_to"
    ACCOUNT ||--|| CARD_XREF : "linked_to"
    CARD ||--o{ TRANSACTION : "processes"
    TRANSACTION }o--|| TRANSACTION_TYPE : "categorized_by"
    ACCOUNT ||--o{ TRANSACTION_CATEGORY_BALANCE : "tracks"
    TRANSACTION_TYPE ||--o{ TRANSACTION_CATEGORY_BALANCE : "categorizes"
    ACCOUNT }o--|| DISCLOSURE_GROUP : "governed_by"
    TRANSACTION_TYPE ||--o{ DISCLOSURE_GROUP : "defines_terms"
```

## Entity Descriptions

### Core Business Entities

#### CUSTOMER
- **Purpose**: Stores customer demographic and identification information
- **Key Business Rules**: 
  - FICO score must be 300-850
  - SSN must be unique and valid format
  - Phone numbers must follow US format
- **File**: CUSTDATA (VSAM KSDS, 500 bytes)

#### ACCOUNT  
- **Purpose**: Manages credit card accounts and balances
- **Key Business Rules**:
  - Credit limit must be > 0
  - Cash limit ≤ credit limit
  - Account must be linked to valid customer
- **File**: ACCTDATA (VSAM KSDS, 300 bytes)

#### CARD
- **Purpose**: Represents physical/virtual credit cards
- **Key Business Rules**:
  - Card number must be 16 digits and unique
  - Must be linked to active account
  - CVV must be 3 digits
- **File**: CARDDATA (VSAM KSDS, 150 bytes)

### Supporting Entities

#### CARD_XREF
- **Purpose**: Cross-reference for fast card-to-account lookup
- **Key Business Rules**: Maintains referential integrity
- **File**: CARDXREF (VSAM KSDS with AIX)

#### TRANSACTION
- **Purpose**: Records all financial transactions
- **Key Business Rules**:
  - Transaction ID must be unique
  - Amount cannot exceed available credit
  - Must reference valid card
- **File**: TRANSACT (VSAM KSDS, 350 bytes)

#### TRANSACTION_CATEGORY_BALANCE
- **Purpose**: Tracks balances by category for interest calculation
- **Key Business Rules**: Updated during transaction posting
- **File**: TCATBALF (VSAM KSDS)

### Reference Data Entities

#### TRANSACTION_TYPE
- **Purpose**: Defines valid transaction types (01-07)
- **Storage**: DB2 table with VSAM extract

#### DISCLOSURE_GROUP
- **Purpose**: Defines interest rates and fees by account group
- **Key Business Rules**: Used for interest calculation
- **File**: DISCGRP (VSAM KSDS)

#### USER_SECURITY
- **Purpose**: User authentication and authorization
- **Key Business Rules**: Role-based access control
- **File**: USRSEC (VSAM KSDS)

## Relationship Rules

### One-to-Many Relationships
- **Customer → Account**: One customer can have multiple accounts
- **Account → Card**: One account can have multiple cards
- **Card → Transaction**: One card can process multiple transactions

### Cross-Reference Relationships
- **Card ↔ Account**: Via CARD_XREF for fast lookup
- **Account ↔ Disclosure Group**: For interest rate determination

### Referential Integrity
- All foreign keys must reference valid parent records
- Cascade rules apply for certain relationships
- Orphaned records are not allowed in production

## Data Volume Estimates
- **Customers**: 100,000+ records
- **Accounts**: 150,000+ records  
- **Cards**: 200,000+ records
- **Transactions**: 10M+ records annually
- **Category Balances**: 500,000+ records
