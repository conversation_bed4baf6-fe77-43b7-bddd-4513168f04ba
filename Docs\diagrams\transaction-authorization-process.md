# Transaction Authorization Process Flow

## Overview
This diagram shows the real-time transaction authorization process that validates card numbers, checks account status, and enforces credit limits.

## Business Rules Implemented
- Card validation in cross-reference file
- Account status and expiration checking
- Credit limit enforcement
- Error code generation (100, 101, 102, 103)

## Mermaid Diagram Code

```mermaid
flowchart TD
    A[Transaction Request Received] --> B[Validate Request Format]
    B --> C{Format Valid?}
    C -->|No| D[Return Format Error]
    C -->|Yes| E[Generate Transaction ID]
    E --> F[Lookup Card in CARDXREF]
    F --> G{Card Found?}
    G -->|No| H[Return Error 100<br/>Invalid Card Number]
    G -->|Yes| I[Check Card Status]
    I --> J{Card Active?}
    J -->|No| K[Return Error<br/>Card Inactive]
    J -->|Yes| L[Lookup Account in ACCTDATA]
    L --> M{Account Found?}
    M -->|No| N[Return Error 101<br/>Account Not Found]
    M -->|Yes| O[Check Account Status]
    O --> P{Account Active?}
    P -->|No| Q[Return Error<br/>Account Inactive]
    P -->|Yes| R[Check Account Expiration]
    R --> S{Account Valid?}
    S -->|No| T[Return Error 103<br/>Account Expired]
    S -->|Yes| U[Calculate Available Credit]
    U --> V[Available = Credit Limit - Current Balance]
    V --> W{Amount <= Available?}
    W -->|No| X[Return Error 102<br/>Over Limit]
    W -->|Yes| Y[Approve Transaction]
    Y --> Z[Log Authorization]
    Z --> AA[Return Approval Response]
    
    H --> BB[Log Decline]
    K --> BB
    N --> BB
    Q --> BB
    T --> BB
    X --> BB
    BB --> CC[Return Decline Response]
    
    style A fill:#e1f5fe
    style Y fill:#c8e6c9
    style AA fill:#c8e6c9
    style H fill:#ffcdd2
    style K fill:#ffcdd2
    style N fill:#ffcdd2
    style Q fill:#ffcdd2
    style T fill:#ffcdd2
    style X fill:#ffcdd2
    style CC fill:#ffcdd2
```

## Error Codes
- **100**: Invalid card number (card not found in cross-reference)
- **101**: Account record not found
- **102**: Over limit transaction (insufficient credit)
- **103**: Transaction received after account expiration

## Key Decision Points
1. **Card Validation**: Must exist in CARDXREF file
2. **Account Validation**: Must exist and be active
3. **Credit Check**: Available credit = Credit Limit - Current Balance
4. **Expiration Check**: Transaction date must be before account expiration

## Related Programs
- Authorization validation routines in transaction processing programs
- Card cross-reference lookup functions
- Account balance calculation logic
