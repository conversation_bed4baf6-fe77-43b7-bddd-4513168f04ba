DEFINE MAPSET(COPAU00) GROUP(CARDDEMO)
DESCRIPTION(CREDIT CARD AUTHORIZATION SUMMARY MAP)
       RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
       DEFINETIME(23/03/09 18:09:14) CHANGETIME(23/03/09 18:09:14)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE MAPSET(COPAU01) GROUP(CARDDEMO)
DESCRIPTION(CREDIT CARD AUTHORIZATION DETAILS MAP)
       RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
       DEFINETIME(23/03/09 18:09:26) CHANGETIME(23/03/09 18:09:26)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE PROGRAM(COPAUA0C) GROUP(CARDDEMO)
       LANGUAGE(COBOL) RELOAD(YES) RESIDENT(NO) USAGE(NORMAL)
       USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
       EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
       TRANSID(CP00) EXECUTIONSET(FULLAPI) JVM(NO)
       DEFINETIME(23/03/06 23:47:41) CHANGETIME(23/03/06 23:49:29)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE PROGRAM(COPAUS0C) GROUP(CARDDEMO)
       LANGUAGE(COBOL) RELOAD(YES) RESIDENT(NO) USAGE(NORMAL)
       USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
       EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
       TRANSID(CPVS) EXECUTIONSET(FULLAPI) JVM(NO)
       DEFINETIME(23/03/09 17:34:50) CHANGETIME(23/03/09 17:35:11)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE PROGRAM(COPAUS1C) GROUP(CARDDEMO)
       LANGUAGE(COBOL) RELOAD(YES) RESIDENT(NO) USAGE(NORMAL)
       USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
       EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
       TRANSID(CPVD) EXECUTIONSET(FULLAPI) JVM(NO)
       DEFINETIME(23/03/13 15:32:12) CHANGETIME(23/03/13 15:32:42)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE PROGRAM(COPAUS2C) GROUP(CARDDEMO)
       LANGUAGE(COBOL) RELOAD(YES) RESIDENT(NO) USAGE(NORMAL)
       USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
       EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
       TRANSID(CPVD) EXECUTIONSET(FULLAPI) JVM(NO)
       DEFINETIME(23/03/24 11:15:11) CHANGETIME(23/03/24 11:15:11)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE TRANSACTION(CPVD) GROUP(CARDDEMO)
       PROGRAM(COPAUS1C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
       TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
       RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
       ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
       RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
       CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
       WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
       DEFINETIME(23/03/13 15:32:24) CHANGETIME(23/03/13 15:32:51)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE TRANSACTION(CPVS) GROUP(CARDDEMO)
       PROGRAM(COPAUS0C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
       TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
       RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
       ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
       RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
       CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
       WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
       DEFINETIME(23/03/09 17:34:06) CHANGETIME(23/03/09 17:34:39)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE TRANSACTION(CP00) GROUP(CARDDEMO)
       PROGRAM(COPAUA0C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
       TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
       RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
       ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
       RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
       CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
       WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
       DEFINETIME(23/03/06 23:48:02) CHANGETIME(23/03/06 23:50:09)
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
DEFINE DB2ENTRY(AWS01PLN) GROUP(CARDDEMO)                              
DESCRIPTION(DB2 RETRY FOR AWS01PLN PLAN)                               
       ACCOUNTREC(TXID) AUTHTYPE(USERID) DROLLBACK(YES) PLAN(AWS01PLN) 
       PRIORITY(HIGH) PROTECTNUM(0) THREADLIMIT(1) THREADWAIT(YES)     
       DEFINETIME(22/11/27 19:11:50) CHANGETIME(22/11/27 19:12:39)     
       CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)      
DEFINE DB2TRAN(CPVDTRAN) GROUP(CARDDEMO)                               
DESCRIPTION(DB2TRAN FOR SDB2 TRANSACTION)                              
       ENTRY(AWS01PLN) TRANSID(CPVD) DEFINETIME(23/03/24 11:16:32)     
       CHANGETIME(23/03/24 11:16:53) CHANGEUSRID(AWSUSER)              
       CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)