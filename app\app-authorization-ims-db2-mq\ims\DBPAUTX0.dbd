***********************************************************************
* Copyright Amazon.com, Inc. or its affiliates.
* All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*    http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
* either express or implied. See the License for the specific
* language governing permissions and limitations under the License
***********************************************************************
         TITLE   'ASSEMBLE OF DBDNAME=DBPAUTX0 '
       DBD     NAME=DBPAUTX0,ACCESS=(INDEX,VSAM,PROT),PASSWD=NO,       X
               VERSION=
***********************************************************************
*        DATASET GROUP NUMBER 1                                       *
***********************************************************************
DSG001 DATASET DD1=DDPAUTX0,SIZE=(4096)
***********************************************************************
*        SEGMENT NUMBER 1                                            *
***********************************************************************
       SEGM    NAME=PAUTINDX,PARENT=0,BYTES=6,                         X
               FREQ=100000
       FIELD   NAME=(INDXSEQ,SEQ,U),START=1,BYTES=6,TYPE=P
       LCHILD  NAME=(PAUTSUM0,DBPAUTP0),                               X
               INDEX=ACCNTID
       DBDGEN
       FINISH
       END
