 DEFINE FILE(ACCTDAT) GROUP(CARDDEMO)
        DSNAME(AWS.M2.CARDDEMO.ACCTDATA.VSAM.KSDS) RLSACCESS(NO)
        LSRPOOLNUM(1) READINTEG(UNCOMMITTED) DSNSHARING(ALLREQS)
        STRINGS(1) STATUS(ENABLED) OPENTIME(FIRSTREF) DISPOSITION(SHARE)
        DATABUFFERS(2) INDEXBUFFERS(1) TABLE(NO) MAXNUMRECS(NOLIMIT)
        UPDATEMODEL(LOCKING) LOAD(NO) RECORDFORMAT(V) ADD(YES)
        BROWSE(YES) DELETE(YES) READ(YES) UPDATE(YES) JOURNAL(NO)
        JNLREAD(NONE) JNLSYNCREAD(NO) JNLUPDATE(NO) JNLADD(NONE)
        JNLSYNCWRITE(YES) RECOVERY(NONE) FWDRECOVLOG(NO)
        BACKUPTYPE(STATIC) DEFINETIME(22/05/13 12:56:44)
        CHANGETIME(22/05/13 12:57:33) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE FILE(CARDAIX) GROUP(CARDDEMO)
        DSNAME(AWS.M2.CARDDEMO.CARDDATA.VSAM.AIX.PATH) RLSACCESS(NO)
        LSRPOOLNUM(1) READINTEG(UNCOMMITTED) DSNSHARING(ALLREQS)
        STRINGS(1) STATUS(ENABLED) OPENTIME(FIRSTREF) DISPOSITION(SHARE)
        DATABUFFERS(2) INDEXBUFFERS(1) TABLE(NO) MAXNUMRECS(NOLIMIT)
        UPDATEMODEL(LOCKING) LOAD(NO) RECORDFORMAT(V) ADD(YES)
        BROWSE(YES) DELETE(YES) READ(YES) UPDATE(YES) JOURNAL(NO)
        JNLREAD(NONE) JNLSYNCREAD(NO) JNLUPDATE(NO) JNLADD(NONE)
        JNLSYNCWRITE(YES) RECOVERY(NONE) FWDRECOVLOG(NO)
        BACKUPTYPE(STATIC) DEFINETIME(22/04/01 16:50:32)
        CHANGETIME(22/04/01 16:50:32) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDBATCH) CHANGEAGREL(0730)
 DEFINE FILE(CARDDAT) GROUP(CARDDEMO)
        DSNAME(AWS.M2.CARDDEMO.CARDDATA.VSAM.KSDS) RLSACCESS(NO)
        LSRPOOLNUM(1) READINTEG(UNCOMMITTED) DSNSHARING(ALLREQS)
        STRINGS(1) STATUS(ENABLED) OPENTIME(FIRSTREF) DISPOSITION(SHARE)
        DATABUFFERS(2) INDEXBUFFERS(1) TABLE(NO) MAXNUMRECS(NOLIMIT)
        UPDATEMODEL(LOCKING) LOAD(NO) RECORDFORMAT(V) ADD(YES)
        BROWSE(YES) DELETE(YES) READ(YES) UPDATE(YES) JOURNAL(NO)
        JNLREAD(NONE) JNLSYNCREAD(NO) JNLUPDATE(NO) JNLADD(NONE)
        JNLSYNCWRITE(YES) RECOVERY(NONE) FWDRECOVLOG(NO)
        BACKUPTYPE(STATIC) DEFINETIME(22/04/01 16:50:31)
        CHANGETIME(22/04/01 16:50:31) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDBATCH) CHANGEAGREL(0730)
 DEFINE FILE(CCXREF) GROUP(CARDDEMO)
 DESCRIPTION(CARD TO ACCOUNT XREF)
        DSNAME(AWS.M2.CARDDEMO.CARDXREF.VSAM.KSDS) RLSACCESS(NO)
        LSRPOOLNUM(1) READINTEG(UNCOMMITTED) DSNSHARING(ALLREQS)
        STRINGS(1) STATUS(ENABLED) OPENTIME(FIRSTREF) DISPOSITION(SHARE)
        DATABUFFERS(2) INDEXBUFFERS(1) TABLE(NO) MAXNUMRECS(NOLIMIT)
        UPDATEMODEL(LOCKING) LOAD(NO) RECORDFORMAT(V) ADD(YES)
        BROWSE(YES) DELETE(YES) READ(YES) UPDATE(YES) JOURNAL(NO)
        JNLREAD(NONE) JNLSYNCREAD(NO) JNLUPDATE(NO) JNLADD(NONE)
        JNLSYNCWRITE(YES) RECOVERY(NONE) FWDRECOVLOG(NO)
        BACKUPTYPE(STATIC) DEFINETIME(22/07/11 15:08:37)
        CHANGETIME(22/07/11 15:10:41) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE FILE(CUSTDAT) GROUP(CARDDEMO)
 DESCRIPTION(CARDDEMO CUSTOMER DATA)
        DSNAME(AWS.M2.CARDDEMO.CUSTDATA.VSAM.KSDS) RLSACCESS(NO)
        LSRPOOLNUM(1) READINTEG(UNCOMMITTED) DSNSHARING(ALLREQS)
        STRINGS(1) STATUS(ENABLED) OPENTIME(FIRSTREF) DISPOSITION(SHARE)
        DATABUFFERS(2) INDEXBUFFERS(1) TABLE(NO) MAXNUMRECS(NOLIMIT)
        UPDATEMODEL(LOCKING) LOAD(NO) RECORDFORMAT(V) ADD(YES)
        BROWSE(YES) DELETE(YES) READ(YES) UPDATE(YES) JOURNAL(NO)
        JNLREAD(NONE) JNLSYNCREAD(NO) JNLUPDATE(NO) JNLADD(NONE)
        JNLSYNCWRITE(YES) RECOVERY(NONE) FWDRECOVLOG(NO)
        BACKUPTYPE(STATIC) DEFINETIME(22/05/13 17:56:26)
        CHANGETIME(22/05/13 17:56:55) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE FILE(CXACAIX) GROUP(CARDDEMO)
 DESCRIPTION(ALTERNATE INDEX TO CCXREF VIA ACCOUNT KEY)
        DSNAME(AWS.M2.CARDDEMO.CARDXREF.VSAM.AIX.PATH) RLSACCESS(NO)
        LSRPOOLNUM(1) READINTEG(UNCOMMITTED) DSNSHARING(ALLREQS)
        STRINGS(1) STATUS(ENABLED) OPENTIME(FIRSTREF) DISPOSITION(SHARE)
        DATABUFFERS(2) INDEXBUFFERS(1) TABLE(NO) MAXNUMRECS(NOLIMIT)
        UPDATEMODEL(LOCKING) LOAD(NO) RECORDFORMAT(V) ADD(YES)
        BROWSE(YES) DELETE(YES) READ(YES) UPDATE(YES) JOURNAL(NO)
        JNLREAD(NONE) JNLSYNCREAD(NO) JNLUPDATE(NO) JNLADD(NONE)
        JNLSYNCWRITE(YES) RECOVERY(NONE) FWDRECOVLOG(NO)
        BACKUPTYPE(STATIC) DEFINETIME(22/06/07 11:50:47)
        CHANGETIME(22/06/07 11:52:40) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE FILE(TRANSACT) GROUP(CARDDEMO)
        DSNAME(AWS.M2.CARDDEMO.TRANSACT.VSAM.KSDS) RLSACCESS(NO)
        LSRPOOLNUM(1) READINTEG(UNCOMMITTED) DSNSHARING(ALLREQS)
        STRINGS(1) STATUS(ENABLED) OPENTIME(FIRSTREF) DISPOSITION(SHARE)
        DATABUFFERS(2) INDEXBUFFERS(1) TABLE(NO) MAXNUMRECS(NOLIMIT)
        UPDATEMODEL(LOCKING) LOAD(NO) RECORDFORMAT(V) ADD(YES)
        BROWSE(YES) DELETE(YES) READ(YES) UPDATE(YES) JOURNAL(NO)
        JNLREAD(NONE) JNLSYNCREAD(NO) JNLUPDATE(NO) JNLADD(NONE)
        JNLSYNCWRITE(YES) RECOVERY(NONE) FWDRECOVLOG(NO)
        BACKUPTYPE(STATIC) DEFINETIME(22/06/09 21:48:59)
        CHANGETIME(22/06/09 21:48:59) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE FILE(USRSEC) GROUP(CARDDEMO)
        DSNAME(AWS.M2.CARDDEMO.USRSEC.VSAM.KSDS) RLSACCESS(NO)
        LSRPOOLNUM(1) READINTEG(UNCOMMITTED) DSNSHARING(ALLREQS)
        STRINGS(1) STATUS(ENABLED) OPENTIME(FIRSTREF) DISPOSITION(SHARE)
        DATABUFFERS(2) INDEXBUFFERS(1) TABLE(NO) MAXNUMRECS(NOLIMIT)
        UPDATEMODEL(LOCKING) LOAD(NO) RECORDFORMAT(V) ADD(YES)
        BROWSE(YES) DELETE(YES) READ(YES) UPDATE(YES) JOURNAL(NO)
        JNLREAD(NONE) JNLSYNCREAD(NO) JNLUPDATE(NO) JNLADD(NONE)
        JNLSYNCWRITE(YES) RECOVERY(NONE) FWDRECOVLOG(NO)
        BACKUPTYPE(STATIC) DEFINETIME(22/02/19 19:04:04)
        CHANGETIME(22/03/18 16:16:37) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COACTUP) GROUP(CARDDEMO)
 DESCRIPTION(CREDIT CARD ACCOUNT UPDATE MAP)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/06/10 20:03:53) CHANGETIME(22/06/10 20:04:10)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COACTVW) GROUP(CARDDEMO)
 DESCRIPTION(VIEW ACCOUNT)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/05/13 12:54:20) CHANGETIME(22/05/13 12:54:20)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COADM01) GROUP(CARDDEMO)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/03/17 00:19:05) CHANGETIME(22/03/17 00:19:05)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COBIL00) GROUP(CARDDEMO)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/06/13 11:16:51) CHANGETIME(22/06/13 11:16:51)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COCRDLI) GROUP(CARDDEMO)
 DESCRIPTION(LIST CARDS)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/03/08 18:43:53) CHANGETIME(22/03/08 18:43:57)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COCRDSL) GROUP(CARDDEMO)
 DESCRIPTION(SEARCH CARDS)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/03/08 18:43:11) CHANGETIME(22/03/08 18:43:30)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COCRDUP) GROUP(CARDDEMO)
 DESCRIPTION(CREDIT CARD UPDATE MAPSET)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/05/07 16:02:35) CHANGETIME(22/05/07 16:02:35)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COMEN01) GROUP(CARDDEMO)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/03/15 00:25:09) CHANGETIME(22/03/15 00:25:09)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(CORPT00) GROUP(CARDDEMO)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/07/05 17:10:07) CHANGETIME(22/07/05 17:10:07)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COSGN00) GROUP(CARDDEMO)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/02/19 19:48:31) CHANGETIME(22/02/19 19:48:31)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COTRN00) GROUP(CARDDEMO)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/06/09 20:48:42) CHANGETIME(22/06/09 20:48:42)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COTRN01) GROUP(CARDDEMO)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/06/09 21:36:34) CHANGETIME(22/06/09 21:36:34)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COTRN02) GROUP(CARDDEMO)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/07/13 22:39:09) CHANGETIME(22/07/13 22:39:09)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COUSR00) GROUP(CARDDEMO)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/06/09 20:20:13) CHANGETIME(22/06/09 20:20:13)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDBATCH) CHANGEAGREL(0730)
 DEFINE MAPSET(COUSR01) GROUP(CARDDEMO)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/05/02 20:11:37) CHANGETIME(22/05/02 20:11:37)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COUSR02) GROUP(CARDDEMO)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/05/03 19:39:17) CHANGETIME(22/05/03 19:39:17)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE MAPSET(COUSR03) GROUP(CARDDEMO)
        RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO) STATUS(ENABLED)
        DEFINETIME(22/06/09 20:37:46) CHANGETIME(22/06/09 20:37:46)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE PROGRAM(COACTUPC) GROUP(CARDDEMO)
 DESCRIPTION(CREDIT CARD DEMO ACCOUNT UPDATE)
        RELOAD(NO) RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO)
        STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY) EXECKEY(USER)
        CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/06/10 20:02:51)
        CHANGETIME(22/06/10 20:03:18) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE PROGRAM(COACTVWC) GROUP(CARDDEMO)
 DESCRIPTION(VIEW ACCT)
        RELOAD(NO) RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO)
        STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY) EXECKEY(USER)
        CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO) TRANSID(CAVW)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/05/13 12:55:19)
        CHANGETIME(22/05/13 12:55:19) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE PROGRAM(COADM01C) GROUP(CARDDEMO)
        LANGUAGE(COBOL) RELOAD(NO) RESIDENT(NO) USAGE(NORMAL)
        USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
        EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/03/17 00:19:19)
        CHANGETIME(22/03/17 00:19:19) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE PROGRAM(COBIL00C) GROUP(CARDDEMO)
        LANGUAGE(COBOL) RELOAD(NO) RESIDENT(NO) USAGE(NORMAL)
        USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
        EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/06/13 11:17:15)
        CHANGETIME(22/06/13 11:17:15) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE PROGRAM(COCRDLIC) GROUP(CARDDEMO)
 DESCRIPTION(LIST CARDS)
        RELOAD(NO) RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO)
        STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY) EXECKEY(USER)
        CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO) TRANSID(CC00)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/04/01 16:56:57)
        CHANGETIME(22/04/01 16:56:57) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDBATCH) CHANGEAGREL(0730)
 DEFINE PROGRAM(COCRDSEC) GROUP(CARDDEMO)
 DESCRIPTION(CREDIT CARD SEARCH)
        RELOAD(NO) RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO)
        STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY) EXECKEY(USER)
        CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/03/15 10:11:47)
        CHANGETIME(22/03/15 10:11:56) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE PROGRAM(COCRDSLC) GROUP(CARDDEMO)
 DESCRIPTION(VIEW CARD DETAIL)
        RELOAD(NO) RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO)
        STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY) EXECKEY(USER)
        CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO) TRANSID(CCDL)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/04/08 11:16:44)
        CHANGETIME(22/04/08 11:16:44) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDBATCH) CHANGEAGREL(0730)
 DEFINE PROGRAM(COCRDUPC) GROUP(CARDDEMO)
 DESCRIPTION(CREDIT CARD UPDATE SCREEN)
        RELOAD(NO) RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO)
        STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY) EXECKEY(USER)
        CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/05/07 16:01:51)
        CHANGETIME(22/05/07 16:02:05) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE PROGRAM(COMEN01C) GROUP(CARDDEMO)
        LANGUAGE(COBOL) RELOAD(NO) RESIDENT(NO) USAGE(NORMAL)
        USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
        EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/03/15 00:25:32)
        CHANGETIME(22/03/15 00:25:32) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE PROGRAM(CORPT00C) GROUP(CARDDEMO)
        LANGUAGE(COBOL) RELOAD(NO) RESIDENT(NO) USAGE(NORMAL)
        USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
        EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/07/05 17:10:22)
        CHANGETIME(22/07/05 17:10:22) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE PROGRAM(COSGN00C) GROUP(CARDDEMO)
 DESCRIPTION(LOGIN)
        RELOAD(NO) RESIDENT(NO) USAGE(NORMAL) USELPACOPY(NO)
        STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY) EXECKEY(USER)
        CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO) TRANSID(CC00)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/02/19 19:04:04)
        CHANGETIME(22/02/19 19:04:04) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDBATCH) CHANGEAGREL(0730)
 DEFINE PROGRAM(COTRN00C) GROUP(CARDDEMO)
        LANGUAGE(COBOL) RELOAD(NO) RESIDENT(NO) USAGE(NORMAL)
        USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
        EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/06/09 20:50:25)
        CHANGETIME(22/06/09 20:50:25) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE PROGRAM(COTRN01C) GROUP(CARDDEMO)
        LANGUAGE(COBOL) RELOAD(NO) RESIDENT(NO) USAGE(NORMAL)
        USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
        EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/06/09 21:37:04)
        CHANGETIME(22/06/09 21:37:04) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE PROGRAM(COTRN02C) GROUP(CARDDEMO)
        LANGUAGE(COBOL) RELOAD(NO) RESIDENT(NO) USAGE(NORMAL)
        USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
        EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/07/13 22:39:29)
        CHANGETIME(22/07/13 22:39:29) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE PROGRAM(COUSR00C) GROUP(CARDDEMO)
        LANGUAGE(COBOL) RELOAD(NO) RESIDENT(NO) USAGE(NORMAL)
        USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
        EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/06/09 20:20:13)
        CHANGETIME(22/06/09 20:20:13) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDBATCH) CHANGEAGREL(0730)
 DEFINE PROGRAM(COUSR01C) GROUP(CARDDEMO)
        LANGUAGE(COBOL) RELOAD(NO) RESIDENT(NO) USAGE(NORMAL)
        USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
        EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/05/02 20:11:52)
        CHANGETIME(22/05/02 20:11:52) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE PROGRAM(COUSR02C) GROUP(CARDDEMO)
        LANGUAGE(COBOL) RELOAD(NO) RESIDENT(NO) USAGE(NORMAL)
        USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
        EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/05/03 19:39:31)
        CHANGETIME(22/05/03 19:39:31) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE PROGRAM(COUSR03C) GROUP(CARDDEMO)
        LANGUAGE(COBOL) RELOAD(NO) RESIDENT(NO) USAGE(NORMAL)
        USELPACOPY(NO) STATUS(ENABLED) CEDF(YES) DATALOCATION(ANY)
        EXECKEY(USER) CONCURRENCY(QUASIRENT) API(CICSAPI) DYNAMIC(NO)
        EXECUTIONSET(FULLAPI) JVM(NO) DEFINETIME(22/06/09 20:38:26)
        CHANGETIME(22/06/09 20:38:26) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CAUP) GROUP(CARDDEMO)
 DESCRIPTION(CREDIT CARD DEMO ACCOUNT UPDATE)
        PROGRAM(COACTUPC) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/06/10 20:05:10) CHANGETIME(22/06/10 20:29:43)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CAVW) GROUP(CARDDEMO)
        PROGRAM(COACTVWC) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/05/13 12:56:05) CHANGETIME(22/05/13 12:56:05)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CA00) GROUP(CARDDEMO)
        PROGRAM(COADM01C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/03/17 00:19:31) CHANGETIME(22/03/17 00:19:31)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CB00) GROUP(CARDDEMO)
        PROGRAM(COBIL00C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/06/13 11:17:38) CHANGETIME(22/06/13 11:17:38)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CCDL) GROUP(CARDDEMO)
        PROGRAM(COCRDSLC) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/04/08 11:11:43) CHANGETIME(22/04/08 11:11:43)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CCLI) GROUP(CARDDEMO)
        PROGRAM(COCRDLIC) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/04/01 23:39:09) CHANGETIME(22/04/01 23:39:09)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CCUP) GROUP(CARDDEMO)
 DESCRIPTION(CREDIT CARD UPDATE TRANSACTION)
        PROGRAM(COCRDUPC) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/05/07 16:02:59) CHANGETIME(22/05/07 16:03:06)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CC00) GROUP(CARDDEMO)
        PROGRAM(COSGN00C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/02/19 19:04:04) CHANGETIME(22/02/19 19:04:04)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDBATCH) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CDV1) GROUP(CARDDEMO)
 DESCRIPTION(DEVELOPER TRANSACTION - 1)
        PROGRAM(COCRDSEC) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/03/15 10:13:20) CHANGETIME(22/04/01 23:35:42)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CM00) GROUP(CARDDEMO)
        PROGRAM(COMEN01C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/03/15 00:25:46) CHANGETIME(22/03/15 00:25:46)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CR00) GROUP(CARDDEMO)
        PROGRAM(CORPT00C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/07/05 17:10:37) CHANGETIME(22/07/05 17:10:37)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CT00) GROUP(CARDDEMO)
        PROGRAM(COTRN00C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/06/09 20:50:58) CHANGETIME(22/06/09 20:50:58)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CT01) GROUP(CARDDEMO)
        PROGRAM(COTRN01C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/06/09 21:37:28) CHANGETIME(22/06/09 21:37:28)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CT02) GROUP(CARDDEMO)
        PROGRAM(COTRN02C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/07/13 22:39:44) CHANGETIME(22/07/13 22:39:44)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CU00) GROUP(CARDDEMO)
        PROGRAM(COUSR00C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/06/09 20:20:13) CHANGETIME(22/06/09 20:20:13)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDBATCH) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CU01) GROUP(CARDDEMO)
        PROGRAM(COUSR01C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/05/02 20:12:09) CHANGETIME(22/05/02 20:12:09)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CU02) GROUP(CARDDEMO)
        PROGRAM(COUSR02C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/05/03 19:39:42) CHANGETIME(22/05/03 19:39:42)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TRANSACTION(CU03) GROUP(CARDDEMO)
        PROGRAM(COUSR03C) TWASIZE(0) PROFILE(DFHCICST) STATUS(ENABLED)
        TASKDATALOC(ANY) TASKDATAKEY(USER) STORAGECLEAR(NO)
        RUNAWAY(SYSTEM) SHUTDOWN(DISABLED) ISOLATE(YES) DYNAMIC(NO)
        ROUTABLE(NO) PRIORITY(1) TRANCLASS(DFHTCL00) DTIMOUT(NO)
        RESTART(NO) SPURGE(YES) TPURGE(YES) DUMP(YES) TRACE(YES)
        CONFDATA(NO) OTSTIMEOUT(NO) ACTION(BACKOUT) WAIT(YES)
        WAITTIME(0,0,0) RESSEC(NO) CMDSEC(NO)
        DEFINETIME(22/06/09 20:39:05) CHANGETIME(22/06/09 20:39:05)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE LIBRARY(CARDDLIB) GROUP(CARDDEMO)
        RANKING(50) CRITICAL(NO) STATUS(ENABLED)
        DSNAME01(AWS.M2.CARDDEMO.LOADLIB) DEFINETIME(22/02/19 19:04:04)
        CHANGETIME(22/02/19 19:04:04) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDBATCH) CHANGEAGREL(0730)
 DEFINE LIBRARY(COM2DOLL) GROUP(CARDDEMO)
        RANKING(50) CRITICAL(NO) STATUS(DISABLED)
        DSNAME01(AWS.M2.CARDDEMO.LOADLIB) DEFINETIME(22/03/17 09:03:06)
        CHANGETIME(22/03/17 09:03:06) CHANGEUSRID(AWSUSER)
        CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
 DEFINE TDQUEUE(JOBS) GROUP(CARDDEMO)
 DESCRIPTION(SUBMIT JOBS FROM CICS)
        TYPE(EXTRA) DATABUFFERS(1) DDNAME(INREADER) ERROROPTION(IGNORE)
        OPENTIME(INITIAL) TYPEFILE(OUTPUT) RECORDSIZE(80)
        RECORDFORMAT(FIXED) BLOCKFORMAT(UNBLOCKED) DISPOSITION(MOD)
        DEFINETIME(22/07/06 11:28:04) CHANGETIME(22/07/06 15:52:21)
        CHANGEUSRID(AWSUSER) CHANGEAGENT(CSDAPI) CHANGEAGREL(0730)
