//CBADMCDJ JOB (COBOL),'AWSCODR',CLASS=A,MSGCLASS=H,MSGLEVEL=(1,1),
//         NOTIFY=&SYSUID,TIME=1440
//******************************************************************
//* Copyright Amazon.com, Inc. or its affiliates.                   
//* All Rights Reserved.                                            
//*                                                                 
//* Licensed under the Apache License, Version 2.0 (the "License"). 
//* You may not use this file except in compliance with the License.
//* You may obtain a copy of the License at                         
//*                                                                 
//*    http://www.apache.org/licenses/LICENSE-2.0                   
//*                                                                 
//* Unless required by applicable law or agreed to in writing,      
//* software distributed under the License is distributed on an     
//* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,    
//* either express or implied. See the License for the specific     
//* language governing permissions and limitations under the License
//******************************************************************
//*********************************************************************
//****  Create Resources for Card Demo application               ******
//*********************************************************************
//*  ---------------------------
//*  SET PARMS FOR THIS JOB:
//*  ---------------------------
//   SET HLQ=AWS.M2.CARDDEMO
//*
//STEP1   EXEC PGM=DFHCSDUP,REGION=0M,
//          PARM='CSD(READWRITE),PAGESIZE(60),NOCOMPAT'
//STEPLIB  DD  DSN=OEM.CICSTS.V05R06M0.CICS.SDFHLOAD,DISP=SHR
//DFHCSD   DD  UNIT=SYSDA,DISP=SHR,DSN=OEM.CICSTS.DFHCSD
//OUTDD    DD  SYSOUT=*
//SYSPRINT DD  SYSOUT=*
//SYSIN    DD  *,SYMBOLS=JCLONLY
*/********************************************************************/
*/*  CARDDEMO CICS DEFINITIONS                                       */
*/********************************************************************/
* NOTE: INSTALL GROUP(CARDDEMO) - CEDA IN G(CARDDEMO)                *
*     IF YOU ARE RERUNNING THIS, UNCOMMENT THE DELETE COMMAND. *
*
* START CARDDEMO RESOURCES:
*
* DELETE GROUP(CARDDEMO)

  DEFINE LIBRARY(COM2DOLL) GROUP(CARDDEMO)
                DSNAME01(&HLQ..LOADLIB)

* DEFINE TDQUEUE(CSSD) GROUP(CARDDEMO) TYPE(INTRA)
* DEFINE TDQUEUE(IRDC) GROUP(CARDDEMO) TYPE(INTRA)

  DEFINE MAPSET(COSGN00M)   GROUP(CARDDEMO)
         DESCRIPTION(LOGIN SCREEN)

  DEFINE MAPSET(COSGN00M)   GROUP(CARDDEMO)
         DESCRIPTION(LOGIN SCREEN)

  DEFINE MAPSET(COACT00S)   GROUP(CARDDEMO)
         DESCRIPTION(ACCOUNT MENU)
  DEFINE MAPSET(COACTVWS)   GROUP(CARDDEMO)
         DESCRIPTION(VIEW ACCOUNT)
  DEFINE MAPSET(COACTUPS)   GROUP(CARDDEMO)
         DESCRIPTION(UPDATE ACCOUNT)
  DEFINE MAPSET(COACTDES)   GROUP(CARDDEMO)
         DESCRIPTION(DEACTIVATE ACCOUNT)

  DEFINE MAPSET(COACT00S)   GROUP(CARDDEMO)
         DESCRIPTION(CARD MENU)
  DEFINE MAPSET(COACTVWS)   GROUP(CARDDEMO)
         DESCRIPTION(VIEW CARD)
  DEFINE MAPSET(COACTUPS)   GROUP(CARDDEMO)
         DESCRIPTION(UPDATE CARD)
  DEFINE MAPSET(COACTDES)   GROUP(CARDDEMO)
         DESCRIPTION(DEACTIVATE CARD)

  DEFINE MAPSET(COTRN00S)   GROUP(CARDDEMO)
         DESCRIPTION(TRANSACTION)
  DEFINE MAPSET(COTRNVWS)   GROUP(CARDDEMO)
         DESCRIPTION(TRANSACTION REPORT)
  DEFINE MAPSET(COTRNVDS)   GROUP(CARDDEMO)
         DESCRIPTION(TRANSACTION DETAILS)
  DEFINE MAPSET(COTRNATS)   GROUP(CARDDEMO)
         DESCRIPTION(ADD TRANSACTIONS)

  DEFINE MAPSET(COBIL00S)   GROUP(CARDDEMO)
         DESCRIPTION(BILL PAY SETUP)

  DEFINE MAPSET(COADM00S)   GROUP(CARDDEMO)
         DESCRIPTION(ADMIN MENU)

  DEFINE MAPSET(COTSTP1S)   GROUP(CARDDEMO)
         DESCRIPTION(PGM1 TEST)
  DEFINE MAPSET(COTSTP2S)   GROUP(CARDDEMO)
         DESCRIPTION(PGM2 TEST)
  DEFINE MAPSET(COTSTP3S)   GROUP(CARDDEMO)
         DESCRIPTION(PGM3 TEST)
  DEFINE MAPSET(COTSTP4S)   GROUP(CARDDEMO)
         DESCRIPTION(PGM4 TEST)

  DEFINE PROGRAM(COSGN00C) GROUP(CARDDEMO) DA(ANY) TRANSID(CC00)
         DESCRIPTION(LOGIN)

  DEFINE PROGRAM(COACT00C) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(ACCOUNT MAIN MENU)
  DEFINE PROGRAM(COACTVWC) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(VIEW ACCOUNT)
  DEFINE PROGRAM(COACTUPC) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(UPDATE ACCOUNT)
  DEFINE PROGRAM(COACTDEC) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(DEACTIVATE ACCOUNT)

  DEFINE PROGRAM(COACT00C) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(CARD MENU)
  DEFINE PROGRAM(COACTVWC) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(VIEW CARD)
  DEFINE PROGRAM(COACTUPC) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(UPDATE CARD)
  DEFINE PROGRAM(COACTDEC) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(DEACTIVATE CARD)
  DEFINE PROGRAM(COTRN00C) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(TRANSACTION)
  DEFINE PROGRAM(COTRNVWC) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(TRANSACTION REPORT)
  DEFINE PROGRAM(COTRNVDC) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(TRANSACTION DETAILS)
  DEFINE PROGRAM(COTRNATC) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(ADD TRANSACTIONS)

  DEFINE PROGRAM(COBIL00C) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(BILL PAY)

  DEFINE PROGRAM(COADM00C) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(ADMIN MENU)
         TRANSID(CCAD)

  DEFINE PROGRAM(COTSTP1C) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(PGM1 TEST)
         TRANSID(CCT1)
  DEFINE PROGRAM(COTSTP2C) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(PGM2 TEST)
         TRANSID(CCT2)
  DEFINE PROGRAM(COTSTP3C) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(PGM1 TEST)
         TRANSID(CCT3)
  DEFINE PROGRAM(COTSTP4C) GROUP(CARDDEMO) DA(ANY)
         DESCRIPTION(PGM4 TEST)
         TRANSID(CCT4)

  DEFINE TRANSACTION(CCDM) GROUP(CARDDEMO)
                PROGRAM(COADM00C) TASKDATAL(ANY)

  DEFINE TRANSACTION(CCT1) GROUP(CARDDEMO)
                PROGRAM(COTSTP1C) TASKDATAL(ANY)
  DEFINE TRANSACTION(CCT2) GROUP(CARDDEMO)
                PROGRAM(COTSTP2C) TASKDATAL(ANY)
  DEFINE TRANSACTION(CCT3) GROUP(CARDDEMO)
                PROGRAM(COTSTP3C) TASKDATAL(ANY)
  DEFINE TRANSACTION(CCT4) GROUP(CARDDEMO)
                PROGRAM(COTSTP4C) TASKDATAL(ANY)

  LIST   GROUP(CARDDEMO)
*
* END CARDDEMO RESOURCES
*
/*
//
//*
//* Ver: CardDemo_v1.0-70-g193b394-123 Date: 2022-08-22 17:02:44 CDT
//*
