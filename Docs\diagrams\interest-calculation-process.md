# Interest Calculation Process (CBACT04C)

## Overview
This diagram shows the batch process that calculates monthly interest charges on outstanding balances by transaction category and generates interest transactions.

## Business Purpose
- Calculate monthly interest on outstanding balances
- Generate interest transactions automatically
- Update account balances with interest charges
- Process by transaction category for accurate interest calculation

## Mermaid Diagram Code

```mermaid
flowchart TD
    A[Start CBACT04C] --> B[Open All Files]
    B --> C[Initialize Variables]
    C --> D[Read Category Balance File]
    D --> E{End of File?}
    E -->|Yes| F[Update Last Account]
    E -->|No| G{New Account?}
    G -->|Yes| H[Update Previous Account]
    G -->|No| I[Continue Same Account]
    
    H --> J[Reset Total Interest]
    I --> J
    J --> K[Get Default Interest Rate]
    K --> L[Read Disclosure Group]
    L --> M[Calculate Monthly Interest]
    M --> N[Interest = Balance × Rate ÷ 1200]
    N --> O[Add to Total Interest]
    O --> P[Generate Interest Transaction]
    P --> Q[Write Transaction Record]
    Q --> D
    
    F --> R[Display Final Statistics]
    R --> S[Close All Files]
    S --> T[End Process]
    
    H --> U[Read Account for Update]
    U --> V[Add Interest to Balance]
    V --> W[Update Account Record]
    W --> X[Write Updated Account]
    X --> J
    
    style A fill:#e1f5fe
    style M fill:#fff3e0
    style N fill:#fff3e0
    style O fill:#fff3e0
    style P fill:#c8e6c9
    style Q fill:#c8e6c9
    style V fill:#c8e6c9
    style W fill:#c8e6c9
    style X fill:#c8e6c9
    style T fill:#e8f5e8
```

## Interest Calculation Formula
```
Monthly Interest = (Transaction Category Balance × Annual Interest Rate) ÷ 1200

Where:
- Transaction Category Balance = Outstanding balance for specific category
- Annual Interest Rate = Rate from disclosure group (percentage)
- 1200 = 12 months × 100 (converts annual percentage to monthly decimal)
```

## File Processing
- **Input**: TCATBALF (Transaction category balance file)
- **Reference**: DISCGRP (Disclosure group for interest rates)
- **Output**: TRANSACT (Interest transactions)
- **Updates**: ACCTFILE (Account balances with interest)

## Processing Logic
1. **Sequential Processing**: Read category balances by account
2. **Account Grouping**: Process all categories for each account
3. **Interest Accumulation**: Sum interest across all categories per account
4. **Account Update**: Add total interest to account balance
5. **Transaction Generation**: Create interest transaction records

## Business Rules
- Interest calculated monthly on outstanding balances
- Different rates may apply to different transaction categories
- Interest transactions are automatically generated
- Account balances updated with interest charges
- Processing by account ensures atomic updates

## Related Programs
- **CBACT04C**: Main interest calculation program
- **1200-A-GET-DEFAULT-INT-RATE**: Interest rate retrieval
- **1300-COMPUTE-INTEREST**: Interest calculation routine
- **1050-UPDATE-ACCOUNT**: Account balance update routine
