***********************************************************************
* Copyright Amazon.com, Inc. or its affiliates.
* All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*    http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
* either express or implied. See the License for the specific
* language governing permissions and limitations under the License
***********************************************************************
         TITLE   'ASSEMBLE OF DBDNAME=DBPAUTP0 '
       DBD     NAME=DBPAUTP0,ACCESS=(HIDAM,VSAM),PASSWD=NO,            C
               EXIT=(*,KEY,DATA,NOPATH,(NOCASCADE),LOG),               C
               VERSION=
***********************************************************************
*        DATASET GROUP NUMBER 1                                       *
***********************************************************************
DSG001 DATASET DD1=DDPAUTP0,SIZE=(4096),SCAN=3
***********************************************************************
*        SEGMENT NUMBER 1 - ROOT, PENDING AUTHORIZATION SUMMARY      *
***********************************************************************
       SEGM    NAME=PAUTSUM0,PARENT=0,BYTES=100,RULES=(,HERE),         X
               POINTER=(TWINBWD)
       FIELD   NAME=(ACCNTID,SEQ,U),START=1,BYTES=6,TYPE=P
       LCHILD  NAME=(PAUTINDX,DBPAUTX0),                               X
               POINTER=INDX
***********************************************************************
*        SEGMENT NUMBER 2 - PENDING AUTHORIZATION DETAILS            *
***********************************************************************
       SEGM    NAME=PAUTDTL1,PARENT=((PAUTSUM0,)),BYTES=200
       FIELD   NAME=(PAUT9CTS,SEQ,U),START=1,BYTES=8,TYPE=C
***********************************************************************
       DBDGEN
       FINISH
       END
